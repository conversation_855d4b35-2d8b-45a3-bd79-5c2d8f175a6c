import { DEFAULT_PER_PAGE_PAGINATION, DictCode, SupplierCallType } from "@/constants";
import { getSupplierCallListByPage } from "@/services/app/Supplier/supplier-call";
import Util, { nl2br, sEllipsed, sn } from "@/util";
import { EyeOutlined, InfoCircleOutlined } from "@ant-design/icons";
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProTable,
} from "@ant-design/pro-components";
import { Card, Col, message, Popover, Row, Space, Tag, Typography } from "antd";
import { useTheme } from "antd-style";
import { useEffect, useRef, useState } from "react";
import OrgOfferCommentListModal from "./components/OrgOfferCommentListModal";
import SNotesViewerModal from "@/components/SNotesViewerModal";
import { DefaultContactIconComp, OrgAOptions } from "../Supplier/Supplier";
import { useModel } from "@umijs/max";
import CustomerSelectModal from "../Customer/Customer/components/CustomerSelectModal";
import { assignCustomersToOffer } from "@/services/app/Customer/customer";
import CustomerSelectToSelectContactsModal from "./components/CustomerSelectToSelectContactsModal";
import { OfferExtStatus, OfferExtStatusKv } from "./OrgOfferDetail";

type RowType = API.OfferBlog & {
  uid?: string;
};
type SearchFormValueType = API.OfferBlog;

const OfferBlog: React.FC = () => {
  const theme = useTheme();
  const { getDictByCode } = useModel("app-settings");

  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<RowType>();
  const [openOrgOfferCommentListModal, setOpenOrgOfferCommentListModal] = useState(false);

  // Customer selection modal
  const [openCustomerSelectionModal, setOpenCustomerSelectionModal] = useState(false);

  const [isFromOrg, setIsFromOrg] = useState(false);

  // Add state for the new modal
  const [notesModalVisible, setNotesModalVisible] = useState<boolean>(false);

  // bulk customer selection and emailing
  const [openCustomerSelectToSelectContactsModal, setOpenCustomerSelectToSelectContactsModal] = useState<boolean>(false);

  useEffect(() => {
    const formValues = Util.getSfValues("sf_supplier", {});
    searchFormRef.current?.resetFields();
    searchFormRef.current?.setFieldsValue(formValues);
    setIsFromOrg(!!formValues.fromOrg);
  }, []);

  const hideCompany = !!sn(getDictByCode(DictCode.OFFER_HIDE_COMPANY));
  const columns: ProColumns<RowType>[] = [
    {
      title: "Company",
      dataIndex: ["supplier", "name"],
      width: 200,
      ellipsis: true,
      hideInTable: hideCompany,
      render(__, entity) {
        const id = entity.uid_type == "call" ? entity.supplier_id : (entity.supplier as any)?.supp_supplier_id;
        return id ? (
          <Popover
            title="Contacts"
            styles={{ root: { maxWidth: 500 } }}
            placement="right"
            content={
              <>
                {entity?.supplier?.contacts?.map((x) => {
                  const styleDefaultColor = { style: { color: x.is_default ? "black" : "grey" } };
                  return (
                    <Row key={x.id}>
                      <Col flex={"20px"}>
                        <DefaultContactIconComp is_default={x.is_default} />
                      </Col>
                      <Col flex="auto" {...styleDefaultColor}>
                        {x.fullname}
                      </Col>
                      <Col flex={"24px"}>
                        {!!x.note && (
                          <Popover content={x.note} styles={{ body: { maxWidth: 300 } }}>
                            <InfoCircleOutlined {...styleDefaultColor} />
                          </Popover>
                        )}
                      </Col>
                      <Col flex="220px">
                        <Typography.Text copyable style={{ opacity: x.is_default ? 1 : 0.6 }}>
                          {x.email}
                        </Typography.Text>
                      </Col>
                      <Col flex="auto" {...styleDefaultColor}>
                        {x.telephone}
                      </Col>
                    </Row>
                  );
                })}
              </>
            }
          >
            <Typography.Link href={`${PUBLIC_PATH}suppliers/detail/${id}`} target="_blank">
              {entity.supplier?.name || " - "}
            </Typography.Link>
          </Popover>
        ) : (
          entity.supplier?.name || " - "
        );
      },
    },
    {
      title: "Org A",
      dataIndex: ["supplier", "org_a"],
      width: 30,
      align: "center",
      ellipsis: true,
      tooltip: "OrgA in WHC_Org",
    },
    {
      title: "Created on",
      dataIndex: ["created_on"],
      sorter: true,
      width: 120,
      align: "center",
      ellipsis: true,
      render: (__, record) => Util.dtToDMYHHMMTz(record.created_on),
    },
    {
      title: "Offer No",
      dataIndex: ["offer_no"],
      ellipsis: true,
      width: 120,
      sorter: true,
      defaultSortOrder: "descend",
      render: (__, entity) => {
        return entity.uid_type == "call" ? null : (
          <Typography.Link href={`${PUBLIC_PATH}offers/detail/${entity.offer_no}`} target="_blank">
            {entity.offer_no}
          </Typography.Link>
        );
      },
    },
    {
      title: "Comments",
      dataIndex: ["comment"],
      ellipsis: true,
      width: 300,
      tooltip: "Comments or Latest comments in WHC_Org",
      hideInTable: isFromOrg,
      render(dom, entity) {
        if (entity.uid_type == "call") return dom;
        else if (entity.uid_type == "org_offer") {
          return (
            <Row wrap={false}>
              <Col flex="auto">
                {entity.top3_comments?.map((x) => (
                  <Row key={x.id} className="text-sm" wrap={false}>
                    <Col flex="100px">{Util.dtToDMYHHMM(x.created_on)}</Col>
                    <Col>
                      <Popover title={x.sys_config?.name} content={x.comment} styles={{ root: { maxWidth: 600 } }}>
                        {sEllipsed(x.comment, 30)}
                      </Popover>
                    </Col>
                  </Row>
                ))}
              </Col>
              <Col flex="20px">
                <EyeOutlined
                  title="View all comments"
                  onClick={() => {
                    setCurrentRow(entity);
                    setOpenOrgOfferCommentListModal(true);
                  }}
                />
              </Col>
            </Row>
          );
        }
      },
    },
    {
      title: "Notes",
      dataIndex: ["note"],
      ellipsis: true,
      width: 300,
      render: (__, record) => {
        return record?.note ? (
          <Row wrap={false}>
            <Col flex={"auto"}>
              <Typography.Text ellipsis>{Util.stripTags(record.note)}</Typography.Text>
            </Col>
            <Col flex="20px">
              <Popover
                title={`Offer Notes`}
                trigger={["hover"]}
                content={<div dangerouslySetInnerHTML={{ __html: nl2br(record.note) }} style={{ maxHeight: 600, overflowY: "auto" }} />}
                styles={{ root: { maxWidth: 1200 } }}
                placement="left"
              >
                <div>
                  <EyeOutlined
                    className="c-blue cursor-pointer"
                    onClick={() => {
                      setCurrentRow(record);
                      setNotesModalVisible(true);
                    }}
                  />
                </div>
              </Popover>
            </Col>
          </Row>
        ) : null;
      },
    },
    {
      title: "Status",
      dataIndex: ["status"],
      tooltip: "Status in WHC_ORG",
      ellipsis: true,
      width: 100,
      align: "center",
      hideInTable: !isFromOrg,
      render: (__, record) => {
        return <OfferExtStatusComp status={record.status} />;
      },
    },

    {
      title: "Brand",
      dataIndex: ["ext", "is_brand"],
      ellipsis: true,
      width: 40,
      align: "center",
      hideInTable: !isFromOrg,
      render: (__, record) => {
        return (record as APIOrg.Offer).ext?.is_brand ? "Y" : "";
      },
    },
    {
      title: "B-Group appr.",
      dataIndex: ["ext", "is_b_group_appr"],
      ellipsis: true,
      width: 40,
      align: "center",
      hideInTable: !isFromOrg,
      render: (__, record) => {
        return (record as APIOrg.Offer).ext?.is_b_group_appr ? "Y" : "";
      },
    },

    /* {
      title: "WE",
      dataIndex: ["we"],
      tooltip: "WE in WHC_ORG",
      ellipsis: true,
      hideInTable: !isFromOrg,
      width: 60,
      render: (__, record) => {
        return record.we;
      },
    }, */
    /* {
      title: "",
      dataIndex: ["customer_selection"],
      ellipsis: true,
      hideInTable: !isFromOrg,
      width: 60,
      render: (__, record) => {
        return (
          <Button
            type="primary"
            ghost
            icon={<SelectOutlined />}
            title="Select customer contacts"
            onClick={() => {
              setCurrentRow(record);
              setOpenCustomerSelectToSelectContactsModal(true);
            }}
          />
        );
      },
    }, */
    {
      valueType: "option",
    },
  ];

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            render(props, dom) {
              return <div style={{ marginLeft: "auto" }}>{dom}</div>;
            },
            searchConfig: { submitText: "Search" },
            submitButtonProps: { loading, htmlType: "submit", style: { marginLeft: 8 } },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => {
              searchFormRef.current?.resetFields();
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormSwitch
            name="fromOrg"
            label="From WHC_Org?"
            fieldProps={{
              checked: isFromOrg,
              onChange(checked) {
                setIsFromOrg(checked);
                actionRef.current?.reload();
              },
            }}
          />

          <ProFormSelect
            name="org_a"
            label="Org A"
            options={OrgAOptions}
            fieldProps={{
              onChange(value, option) {
                actionRef.current?.reload();
              },
              popupMatchSelectWidth: false,
            }}
            width={60}
            showSearch
            allowClear
          />

          {/* <ProFormSelect
            name="trademark_id"
            label="Trademark"
            request={getTrademarkACList}
            width="sm"
            disabled={isFromOrg}
          />
          <ProFormSelect
            name="product_id"
            label="Product"
            request={getProductACList}
            width="sm"
            disabled={isFromOrg}
          /> */}

          <ProFormDigit name={"offer_no"} label="Offer No" width={"xs"} placeholder={"Offer No"} />

          <ProFormText name={"comment"} label="Comment" width={"sm"} placeholder={"Search by Comment"} />

          <ProFormText name={"keyWords"} label="KeyWords" width={200} placeholder={"Search by Trademark / Products / Comment / Notes"} />
        </ProForm>
      </Card>

      <ProTable<RowType, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>Offers List</span>
          </Space>
        }
        actionRef={actionRef}
        rowKey="uid"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{
          defaultPageSize: sn(Util.getSfValues("sf_supplier_p")?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues("sf_supplier", searchFormValues);
          Util.setSfValues("sf_supplier_p", params);

          setLoading(true);
          return getSupplierCallListByPage(
            {
              ...params,
              ...searchFormValues,
              in_type: [SupplierCallType.Offer],
              with: "supplier,products,trademarks,mergeOrgOffer",
            },
            sort,
            filter,
          )
            .then((res) => {
              if (currentRow?.id) {
                setCurrentRow(res.data.find((x) => x.id == currentRow.id));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        /* rowSelection={{
          selectedRowKeys: selectedRows?.map((x) => x.id as React.Key),
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }} */
        onRequestError={Util.error}
        columns={columns}
        tableAlertRender={false}
        columnEmptyText=""
      />

      {!!currentRow?.org_offer_id && (
        <OrgOfferCommentListModal
          offer_id={currentRow?.org_offer_id}
          modalVisible={openOrgOfferCommentListModal}
          handleModalVisible={setOpenOrgOfferCommentListModal}
        />
      )}

      <SNotesViewerModal id="notes-viewer" content={currentRow?.note} modalVisible={notesModalVisible} handleModalVisible={setNotesModalVisible} />

      <CustomerSelectModal
        modalVisible={openCustomerSelectionModal}
        handleModalVisible={setOpenCustomerSelectionModal}
        searchParams={{ byOfferNo: currentRow?.offer_no }}
        addCustomer={async (customerIds, mode) => {
          if (customerIds?.length) {
            const hide = message.loading("Updating...", 0);
            return assignCustomersToOffer(currentRow?.offer_no, customerIds, mode)
              .then((res) => {
                message.success("Saved successfully.");
                setOpenCustomerSelectionModal(false);
                actionRef.current?.reload();
              })
              .catch(Util.error)
              .finally(hide);
          }
        }}
      />

      <CustomerSelectToSelectContactsModal
        searchParams={{ offerNo: currentRow?.offer_no }}
        modalVisible={openCustomerSelectToSelectContactsModal}
        handleModalVisible={setOpenCustomerSelectToSelectContactsModal}
      />
    </PageContainer>
  );
};

export default OfferBlog;
