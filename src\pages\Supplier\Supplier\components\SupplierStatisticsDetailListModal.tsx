import { Col, Mo<PERSON>, <PERSON>, <PERSON>, Typography } from "antd";
import React, { Dispatch, SetStateAction, useEffect, useMemo, useRef, useState } from "react";
import type { ProColumns, ActionType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";

import { CURRENT_YEAR, DEFAULT_PER_PAGE_PAGINATION } from "@/constants";
import Util, { nf2, ni, sn } from "@/util";
import type { ProFormInstance } from "@ant-design/pro-form";
import { getOrgInvoiceCheckStatsDetailsList, getSupplierLoInvoiceCheckStatsByPage } from "@/services/app/Supplier/supplier";

type RecordType = APITask.Supplier & APITask.FinDetail;

type SupplierStatisticsDetailListModalProps = {
  searchParams?: { supplier_id?: number; y?: number; ym?: string };
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
};

const SupplierStatisticsDetailListModal: React.FC<SupplierStatisticsDetailListModalProps> = (props) => {
  const { handleModalVisible, modalVisible, searchParams } = props;
  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);

  const columns = useMemo<ProColumns<RecordType>[]>(
    () => [
      {
        title: "Field1",
        dataIndex: "Field1",
        width: 30,
      },
      {
        title: "Re_Nr",
        dataIndex: "Re_Nr",
        width: 70,
      },
      {
        title: "Kunde",
        dataIndex: "Kunde",
        width: 150,
        ellipsis: true,
      },
      {
        title: "Datum",
        dataIndex: "Datum",
        width: 80,
        ellipsis: true,
        align: "center",
        render(dom, entity, index, action, schema) {
          return Util.dtToDMY(entity.Datum);
        },
      },
      {
        title: "Artikelnummer",
        dataIndex: "Artikelnummer",
        width: 90,
        ellipsis: true,
      },
      {
        title: "Beschreibung",
        dataIndex: "Beschreibung",
        width: 250,
        ellipsis: true,
      },
      {
        title: "Gepackt",
        dataIndex: "Gepackt",
        width: 80,
        align: "right",
        ellipsis: true,
        render(dom, entity, index, action, schema) {
          return nf2(entity.Gepackt);
        },
      },
      {
        title: "Order",
        dataIndex: "Order",
        width: 80,
        align: "right",
        ellipsis: true,
        render(dom, entity, index, action, schema) {
          return nf2(entity.Order);
        },
      },
      {
        title: "Preis",
        dataIndex: "Preis",
        width: 80,
        align: "right",
        ellipsis: true,
        render(dom, entity, index, action, schema) {
          return nf2(entity.Preis);
        },
      },
      {
        title: "Summe",
        dataIndex: "Summe",
        width: 80,
        align: "right",
        ellipsis: true,
        render(dom, entity, index, action, schema) {
          return nf2(entity.Summe);
        },
      },
      {
        title: "EK",
        dataIndex: "EK",
        width: 80,
        align: "right",
        ellipsis: true,
        render(dom, entity, index, action, schema) {
          return nf2(entity.EK);
        },
      },
      {
        title: "EK_Summe",
        dataIndex: "EK_Summe",
        width: 80,
        align: "right",
        ellipsis: true,
        render(dom, entity, index, action, schema) {
          return nf2(entity.EK_Summe);
        },
      },
      {
        title: "Ertrag",
        dataIndex: "Ertrag",
        width: 80,
        align: "right",
        ellipsis: true,
        render(dom, entity, index, action, schema) {
          return nf2(entity.Ertrag);
        },
      },
      {
        title: "Supplier",
        dataIndex: "supplier_name",
        width: 80,
        ellipsis: true,
        render(dom, entity, index, action, schema) {
          return entity.supplier_name;
        },
      },
    ],
    [],
  );

  useEffect(() => {
    if (modalVisible) {
      actionRef.current?.reload();
    }
  }, [modalVisible]);

  return (
    <Modal
      title={<>Fin Details</>}
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width="1400px"
      footer={false}
      styles={{ body: { paddingTop: 0 } }}
    >
      <ProTable<RecordType, API.PageParams>
        headerTitle={false}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={false}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{
          defaultPageSize: sn(Util.getSfValues("supplier_stats_p")?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues("supplier_stats", searchFormValues);
          Util.setSfValues("supplier_stats_p", params);

          setLoading(true);
          return getOrgInvoiceCheckStatsDetailsList(
            {
              ...searchParams,
              ...params,
              ...searchFormValues,
              with: "org_supplier,contacts,address,address.country,ext,ext.user,meta,metaLabel,calls_phone_count",
            },
            sort,
            filter,
          )
            .then((res) => {
              return res as RecordType;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        tableAlertRender={false}
        columnEmptyText=""
        locale={{ emptyText: <></> }}
      />
    </Modal>
  );
};

export default SupplierStatisticsDetailListModal;
