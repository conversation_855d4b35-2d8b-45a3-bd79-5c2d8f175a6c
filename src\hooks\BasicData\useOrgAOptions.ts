import { getOrgAList } from '@/services/app/Supplier/supplier';
import Util from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import React, { useCallback, useEffect, useState } from 'react';

/**
 * Auto completion list of Active User
 */
export default (
    defaultParams?: Record<string, any>,
    formRef?: React.MutableRefObject<ProFormInstance | undefined>,
    eleOptions?: any,
) => {
    const [loading, setLoading] = useState<boolean>(false);
    const [orgAOptionsKv, setOrgAOptionsKv] = useState<Record<string, string>>({});

    const searchOrgAOptions = useCallback(async (params?: Record<string, any>) => {
        setLoading(true);
        getOrgAList(params ?? {})
            .then((res) => {
                setOrgAOptionsKv({ 'all': 'All', '-': "N/A", ...res });
            })
            .catch(Util.error)
            .finally(() => {
                setLoading(false);
            })
            ;
    }, []);

    useEffect(() => {
        searchOrgAOptions();
    }, [searchOrgAOptions]);

    return {
        orgAOptionsKv,
        searchOrgAOptions,
        loading,
    };
};
