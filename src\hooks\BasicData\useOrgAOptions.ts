import { getOrgAList } from '@/services/app/Supplier/supplier';
import Util from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import { DefaultOptionType } from 'antd/es/select';
import React, { useCallback, useEffect, useState } from 'react';

/**
 * Auto completion list of Active User
 */
export default (
    defaultParams?: Record<string, any>,
    formRef?: React.MutableRefObject<ProFormInstance | undefined>,
    eleOptions?: any,
) => {
    const [loading, setLoading] = useState<boolean>(false);
    const [orgAOptionsKv, setOrgAOptionsKv] = useState<Record<string, string>>({});
    const [orgAOptions, setOrgAOptions] = useState<DefaultOptionType[]>([]);
    const [selectedOrgA, setSelectedOrgA] = useState<any>(null);

    const searchOrgAOptions = useCallback(async (params?: Record<string, any>) => {
        setLoading(true);
        getOrgAList(params ?? {})
            .then((res) => {
                setOrgAOptionsKv({ 'all': 'All', '-': "N/A", ...res });
                setOrgAOptions([{ value: 'all', label: 'All' }, { value: '-', label: 'N/A' }, ...Object.keys(res).map(x => ({ value: x, label: res[x] }))]);
            })
            .catch(Util.error)
            .finally(() => {
                setLoading(false);
            })
            ;
    }, []);

    useEffect(() => {
        searchOrgAOptions();
    }, [searchOrgAOptions]);

    useEffect(() => {
        setOrgAOptions(prev => {
            const newPrev = [...prev];
            if (selectedOrgA?.includes('all')) {
                newPrev.forEach((x) => {
                    if (x.value != 'all') {
                        x.disabled = true;
                    }
                });
            } else {
                newPrev.forEach((x) => {
                    if (x.value != 'all') {
                        x.disabled = false;
                    }
                });
            }

            return newPrev;
        })
    }, [selectedOrgA]);

    return {
        orgAOptionsKv,
        searchOrgAOptions,
        loading,
        selectedOrgA,
        setSelectedOrgA,
        orgAOptions,
    };
};
