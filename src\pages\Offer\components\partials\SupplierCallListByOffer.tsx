import { DT_FORMAT_DM, SupplierCallDirection, SupplierCallDirectionKv, SupplierCallType, SupplierCallTypeKv } from "@/constants";
import { deleteSupplierCall, getSupplierCallListByPage } from "@/services/app/Supplier/supplier-call";
import Util, { nl2br, sEllipsed, sn, urlFull } from "@/util";
import { DeleteOutlined, EditOutlined, SearchOutlined } from "@ant-design/icons";
import { ActionType, ProColumns, ProForm, ProFormInstance, ProFormText, ProTable } from "@ant-design/pro-components";
import { Button, Col, message, Popconfirm, Popover, Row, Space, Typography } from "antd";
import { useEffect, useRef, useState } from "react";
import CreateEmailForm from "@/pages/Email/EmailList/components/CreateEmailForm";

import SNotesViewerModal from "@/components/SNotesViewerModal";
import { SupplierCallTypeComp } from "@/pages/Supplier/Supplier/components/SupplierCallList";
import SupplierCallCreateForm from "./SupplierCallCreateForm";
import SupplierCallUpdateForm from "./SupplierCallUpdateForm";

export type SearchFormValueType = Partial<API.SupplierCall>;

type RowType = API.SupplierCall;

type SupplierCallListByOfferProps = {
  supplier?: API.Supplier;
  offer_no?: string | number;
};

const SupplierCallListByOffer: React.FC<SupplierCallListByOfferProps> = (props) => {
  const { supplier, offer_no } = props;
  const { id, ext: supplierExt } = supplier || {};

  const actionRef = useRef<ActionType>();

  const [currentRow, setCurrentRow] = useState<RowType>();
  const [loading, setLoading] = useState(false);

  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const [replyModalVisible, handleReplyModalVisible] = useState<boolean>(false);
  const [openStaffEmailModal, setOpenStaffEmailModal] = useState<boolean>(false);

  const [inc_internal_email, set_inc_internal_email] = useState<boolean>(false);
  const [notesModalVisible, setNotesModalVisible] = useState<boolean>(false);

  const [totalRecordsCount, setTotalRecordsCount] = useState<number>(0);

  // Search form
  const searchFormRef = useRef<ProFormInstance>();

  const columns: ProColumns<RowType>[] = [
    {
      title: "Created on",
      dataIndex: ["date"],
      defaultSortOrder: "descend",
      sorter: true,
      width: 35,
      align: "center",
      ellipsis: true,
      render: (__, record) => Util.dtToDMY(record.date, DT_FORMAT_DM),
    },
    {
      title: "Type",
      dataIndex: ["type"],
      sorter: false,
      width: 20,
      ellipsis: true,
      align: "center",
      render: (__, record) => {
        return <SupplierCallTypeComp type={record.type} internal={record.uid?.startsWith("call_")} />;
      },
    },
    /* {
      title: "",
      dataIndex: ["direction"],
      sorter: false,
      width: 30,
      ellipsis: true,
      align: "center",
      className: "c-grey text-sm",
      render: (__, record) => {
        return record.direction ? (SupplierCallDirectionKv[record.direction] ?? record.direction) : null;
      },
    }, */
    {
      title: "Notes",
      dataIndex: ["note"],
      width: 280,
      render: (__, record) => {
        return record?.note ? (
          <Row wrap={false}>
            <Col flex={"auto"}>
              <Popover
                title={`${SupplierCallTypeKv[record.type || 0]}${record.direction ? ` / ${SupplierCallDirectionKv[record.direction]}` : ""}`}
                content={<div dangerouslySetInnerHTML={{ __html: nl2br(record.note) }} style={{ maxHeight: 600, overflowY: "auto" }}></div>}
                trigger={["hover"]}
                styles={{ root: { maxWidth: 1200 } }}
              >
                <Typography.Paragraph
                  onClick={() => {
                    setCurrentRow(record);
                    setNotesModalVisible(true);
                  }}
                  className="cursor-pointer"
                  ellipsis={{
                    rows: 2,
                    expanded: false,
                  }}
                  style={{ marginBottom: 0 }}
                >
                  {sEllipsed(Util.stripTags(record.note), 200)}
                </Typography.Paragraph>
              </Popover>
            </Col>
          </Row>
        ) : null;
      },
    },
    /* {
      title: "Subject / Comment",
      dataIndex: ["email_subject"],
      width: 300,
      ellipsis: true,
      render(__, entity) {
        return entity.uid?.includes("call_") ? entity.comment : entity.email_subject;
      },
    }, */
    {
      valueType: "option",
      fixed: "right",
      width: 40,
      className: "p-0",
      render(__, entity) {
        return (
          <Space.Compact>
            {entity.uid?.startsWith("call_") && (
              <Button
                type="link"
                color="primary"
                icon={<EditOutlined color="primary" />}
                style={{ width: 20 }}
                onClick={() => {
                  setCurrentRow(entity);
                  handleUpdateModalVisible(true);
                }}
              />
            )}
            {entity.uid?.startsWith("call_") && (
              <Popconfirm
                className="cursor-pointer c-red"
                title={<div>Are you sure you want to delete?</div>}
                okText="Yes"
                cancelText="No"
                styles={{ root: { width: 300 } }}
                onConfirm={async () => {
                  const hide = message.loading("Deleting a selected log...", 0);
                  deleteSupplierCall(entity.id)
                    .then(() => {
                      message.success("Deleted successfully.");
                      actionRef.current?.reload();
                    })
                    .catch(Util.error)
                    .finally(() => hide());
                }}
              >
                <div>
                  <Button type="link" danger icon={<DeleteOutlined color="danger" />} style={{ width: 20 }} />
                </div>
              </Popconfirm>
            )}
          </Space.Compact>
        );
      },
    },
  ];

  useEffect(() => {
    if (id) {
      actionRef.current?.reload();
    }
  }, [id]);

  /* const { userOptions } = useUserOptions();
  const userId = supplierExt?.user?.user_id;
  const initials = supplierExt?.user?.initials ?? supplierExt?.user?.username ?? "Select"; */

  useEffect(() => {
    actionRef.current?.reload();
  }, [inc_internal_email]);

  return (
    <>
      <ProTable<RowType, API.PageParams>
        headerTitle={
          <Space size={8}>
            <div>Supplier: </div>
            <ProForm<SearchFormValueType>
              layout="inline"
              formRef={searchFormRef}
              isKeyPressSubmit
              submitter={{
                render(props, dom) {
                  return <div style={{ marginLeft: "auto" }}>{dom}</div>;
                },
                searchConfig: { submitText: <SearchOutlined /> },
                submitButtonProps: { htmlType: "submit", type: "default" },
                resetButtonProps: { style: { display: "none" } },
                onSubmit: () => actionRef.current?.reload(),
              }}
            >
              <ProFormText name="ft_note" placeholder="Search Notes..." width={120} />
            </ProForm>
            {/* <div style={{ fontWeight: "normal" }}>
              <Checkbox
                id="inc_internal_email"
                title="Include Internal Email?"
                defaultChecked={false}
                checked={inc_internal_email}
                onChange={(e) => set_inc_internal_email(e.target.checked)}
              />
            </div> */}
            {/* <div style={{ fontWeight: "normal" }}>
              <label htmlFor="offer_mode" style={{ marginRight: 8 }}>
                Offer
              </label>
              <Radio.Group
                id="offer_mode"
                onChange={(e) => set_offer_mode(e.target.value)}
                value={offer_mode}
                options={[
                  { value: "inc", label: "Inc" },
                  { value: "exc", label: "Exc" },
                  { value: "only", label: "Only" },
                ]}
              />
            </div> */}
          </Space>
        }
        toolBarRender={() => [
          <Space key="action-buttons" size={48} style={{ marginRight: 8 }}>
            <Button
              key="new"
              type={supplier ? "primary" : "default"}
              onClick={() => {
                if (!supplier) {
                  message.info("Please link Supplier to Supplier in WHC Org");
                } else {
                  handleCreateModalVisible(true);
                }
              }}
            >
              New
            </Button>
            {/* <Space>
              <Button
                key="reply_mail"
                type="primary"
                icon={<MailOutlined />}
                title={`Send email to ${supplier?.name}`}
                onClick={() => {
                  handleReplyModalVisible(true);
                }}
              >
                New Email
              </Button>
              <Button
                key="send_mail_to_staff"
                type="primary"
                icon={<SendOutlined />}
                title={`Send email to ${initials}`}
                onClick={() => {
                  setOpenStaffEmailModal(true);
                }}
              >
                Staff Email
              </Button>
            </Space> */}
          </Space>,
        ]}
        actionRef={actionRef}
        rowKey="uid"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true, density: false, setting: false }}
        search={false}
        sticky
        scroll={{ x: 420 }}
        pagination={{
          defaultPageSize: sn(Util.getSfValues("supp_sf_supplier_call_list_in_offer_detail_p")?.pageSize ?? 20),
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = {};
          Util.setSfValues("supp_sf_supplier_call_list_in_offer_detail", searchFormValues);
          Util.setSfValues("supp_sf_supplier_call_list_in_offer_detail_p", params);

          setLoading(true);
          return getSupplierCallListByPage(
            {
              ...params,
              with: "mergeEmail",
              supplier_id: id,
              inc_internal_email,
              offer_no: offer_no,
              ...searchFormRef.current?.getFieldsValue(),
            },
            sort,
            filter,
          )
            .then((res) => {
              if (currentRow?.id) {
                setCurrentRow(res.data.find((x) => x.id == currentRow.id));
              }

              setTotalRecordsCount(res.total || 0);
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        columnEmptyText=""
        locale={{ emptyText: <></> }}
        cardProps={{ bodyStyle: { padding: 0 }, headStyle: { padding: 0 } }}
        tableStyle={{ display: !totalRecordsCount ? "none" : "" }}
      />

      <SupplierCallCreateForm
        initialValues={{
          supplier_id: id,
          type: SupplierCallType.Phone,
          direction: SupplierCallDirection.Out,
          offer_no: offer_no,
        }}
        modalVisible={createModalVisible}
        handleModalVisible={handleCreateModalVisible}
        onSubmit={(value) => {
          actionRef.current?.reload();
        }}
      />

      <SupplierCallUpdateForm
        initialValues={{ ...currentRow, supplier_id: id, offer_no: offer_no }}
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        onSubmit={(value) => {
          actionRef.current?.reload();
        }}
      />

      <CreateEmailForm
        modalVisible={openStaffEmailModal}
        handleModalVisible={setOpenStaffEmailModal}
        initialValues={{
          receiver: Util.emailBuildSender(supplierExt?.user?.email, supplierExt?.user?.name),
          text_html: `<br /><a href="${urlFull()}suppliers/detail/${supplier?.id}" target="_blank">View Supplier detail: ${supplier?.name} </a>`,
        }}
        htmlEditorId="send_email_whc"
        internal
        supplier={{
          id: supplier?.id,
          name: supplier?.name,
          address: supplier?.address,
          contacts: supplier?.contacts,
          created_on: supplier?.created_on,
        }}
      />

      <CreateEmailForm
        modalVisible={replyModalVisible}
        handleModalVisible={handleReplyModalVisible}
        initialValues={{
          receiver: supplier?.contacts?.[0] ? `${supplier.contacts[0].fullname} <${supplier.contacts[0].email}>` : "",
        }}
        supplier={{
          id: supplier?.id,
          name: supplier?.name,
          address: supplier?.address,
          contacts: supplier?.contacts,
          created_on: supplier?.created_on,
        }}
        onCancel={() => {
          handleReplyModalVisible(false);
        }}
      />

      <SNotesViewerModal
        id="supplier-call-notes-viewer"
        title={`${SupplierCallTypeKv[currentRow?.type || 0]}${currentRow?.direction ? ` / ${SupplierCallDirectionKv[currentRow?.direction]}` : ""} Notes`}
        content={currentRow?.note}
        modalVisible={notesModalVisible}
        handleModalVisible={setNotesModalVisible}
      />
    </>
  );
};

export default SupplierCallListByOffer;
