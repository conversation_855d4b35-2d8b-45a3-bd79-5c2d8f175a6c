import Util from "@/util";
import { PageContainer, ProForm, ProFormCheckbox, ProFormInstance, ProFormRadio, ProFormSwitch } from "@ant-design/pro-components";
import { useModel, useParams } from "@umijs/max";
import { Button, Card, Col, message, Row, Space, Spin, Splitter } from "antd";
import { useCallback, useEffect, useRef, useState } from "react";

import { getOrgOffer } from "@/services/app/Offer/org-offer";
import OfferTemplateLangFormPartial from "../OfferTemplate/components/OfferTemplateLangFormPartial";
import { updateOfferTemplateBulk } from "@/services/app/Offer/offer-template";
import { DictCode } from "@/constants";
import OfferTemplateLangViewPartial from "../OfferTemplate/components/OfferTemplateLangViewPartial";
import ConversationsMixedPanel from "./components/ConversationsMixedPanel";
import CustomerContactSelectModalV2 from "../Customer/Customer/components/CustomerContactSelectModalV2";
import { CloseOutlined } from "@ant-design/icons";
import { getOfferExtByOfferNo, updateOrCreateOfferExt } from "@/services/app/Offer/offer-ext";
import OfferBlogAndNewsletterModal from "./components/OfferBlogAndNewsletterModal";
import { ButtonColorType } from "antd/es/button";

export enum OfferExtStatus {
  CLOSED = 0,
  ACTIVE = 1,
  IN_PROGRESS = 2,
}

export const OfferExtStatusKv: Record<number, string> = {
  [OfferExtStatus.IN_PROGRESS]: "In Progress",
  [OfferExtStatus.ACTIVE]: "Active",
  [OfferExtStatus.CLOSED]: "Closed",
};
export const OfferExtStatusOptions = [
  {
    value: OfferExtStatus.IN_PROGRESS,
    label: OfferExtStatusKv[OfferExtStatus.IN_PROGRESS],
  },
  {
    value: OfferExtStatus.ACTIVE,
    label: OfferExtStatusKv[OfferExtStatus.ACTIVE],
  },
  {
    value: OfferExtStatus.CLOSED,
    label: OfferExtStatusKv[OfferExtStatus.CLOSED],
  },
];

/**
 * Offer details for an offer from WHC_Org
 *
 *
 * @param props
 * @returns
 */
const OrgOfferDetail: React.FC = (props) => {
  const params = useParams();
  const { getDictByCode } = useModel("app-settings");

  const { offer_no } = params || {};

  const formRefExt1 = useRef<ProFormInstance>();
  const formRefTpl = useRef<ProFormInstance>(); // Update offer_template

  const [loading, setLoading] = useState(false);
  const [orgOffer, setOrgOffer] = useState<APIOrg.Offer>();
  const [loadingExt, setLoadingExt] = useState<boolean>(false);
  const [offerExt, setOfferExt] = useState<API.OfferExt>();

  const [viewMode, setViewMode] = useState(0);
  const [rightPanelSize, setRightPanelSize] = useState(33); // percentage
  const [formData, setFormData] = useState();

  const [openCustomerContactSelectionModal, setOpenCustomerContactSelectionModal] = useState<boolean>(false);
  const [openBlogAndNewsletterModal, setOpenBlogAndNewsletterModal] = useState<boolean>(false);

  useEffect(() => {
    setViewMode(Util.getSfValues("org_offer_detail_view_mode", 0));
  }, []);

  /**
   * Load Org Offer and pre-set in form.
   */
  const loadOrgOfferDetail = useCallback(() => {
    if (offer_no) {
      setLoading(true);
      getOrgOffer(offer_no, {
        with: "offerTemplate,blog_cnt,newsletter_cnt,ext",
      })
        .then((res) => {
          setOrgOffer(res);
          const template = res.offer_template;

          formRefTpl.current?.resetFields();
          if (template) {
            const tmp = template.offer_template_langs?.reduce((prev: any, langData) => {
              prev[`${langData.lang}`] = langData;
              return prev;
            }, {});
            tmp.files = template.files?.map((x) => ({ ...x, url: `${API_URL}/api/${x.url}` }));

            setFormData(tmp);
            formRefTpl.current?.setFieldsValue(tmp);
          } else {
            const tmp: any = {
              DE: {
                header: getDictByCode(DictCode.OFFER_SALUTATION_DE),
                footer: getDictByCode(DictCode.OFFER_FOOTER_DE),
              },
              EN: {
                header: getDictByCode(DictCode.OFFER_SALUTATION_EN),
                footer: getDictByCode(DictCode.OFFER_FOOTER_EN),
              },
            };

            setFormData(tmp);
            formRefTpl.current?.setFieldsValue(tmp);
          }
        })
        .catch(Util.error)
        .finally(() => {
          setLoading(false);
        });
    } else {
      setOrgOffer(undefined);
      formRefTpl.current?.resetFields();
      setFormData(undefined);
    }
  }, [getDictByCode, offer_no]);

  useEffect(() => {
    loadOrgOfferDetail();
  }, [loadOrgOfferDetail]);

  const loadOfferExt = useCallback(() => {
    if (orgOffer?.offer_sid) {
      setLoadingExt(true);
      getOfferExtByOfferNo(orgOffer?.offer_sid)
        .then((res) => setOfferExt(res))
        .catch(Util.error)
        .finally(() => {
          setLoadingExt(false);
        });
    } else {
      setOfferExt(undefined);
    }
  }, [orgOffer?.offer_sid]);

  useEffect(() => {
    loadOfferExt();
  }, [loadOfferExt]);

  useEffect(() => {
    if (offerExt) {
      formRefExt1.current?.setFieldsValue(offerExt);
    } else {
      formRefExt1.current?.resetFields();
    }
  }, [offerExt]);

  const handleSave = async () => {
    formRefTpl.current
      ?.validateFields()
      .then(async (__) => {
        const hide = message.loading("Saving...", 0);
        try {
          const values = formRefTpl.current?.getFieldsValue();
          const formData = new FormData();
          // Process each language section (DE, EN, etc.)
          Object.entries(values).forEach(([lang, langData]: [string, any]) => {
            if (lang == "EN" || lang == "DE") {
              // Add basic text fields
              if (langData.subject) {
                formData.append(`data[${lang}][subject]`, langData.subject);
              }
              if (langData.header) {
                formData.append(`data[${lang}][header]`, langData.header);
              }
              if (langData.body) {
                formData.append(`data[${lang}][body]`, langData.body);
              }
              if (langData.footer) {
                formData.append(`data[${lang}][footer]`, langData.footer);
              }

              formData.append(`data[${lang}][lang]`, lang);
            }
          });

          console.log("Step 1");

          // Handle file uploads
          if (values.files?.length) {
            values.files.forEach((file: any, index: number) => {
              // Only append file if it has originFileObj (new file)
              if (file?.originFileObj) {
                formData.append(`data[files][${index}]`, file.originFileObj);
              } else if (file.id) {
                // For existing files, just pass the ID
                formData.append(`data[existing_files][${index}]`, file.id);
              }
            });
          }
          if (offer_no) {
            formData.append("offer_no", offer_no);
          }

          // Call API to update offer templates
          const result = await updateOfferTemplateBulk(formData);

          hide();
          message.success("Templates saved successfully");

          loadOrgOfferDetail();

          return result;
        } catch (error) {
          hide();
          Util.error(error);
          return false;
        }
      })
      .catch((err) => {
        Util.error("There are validation errors in the form. Please check them out!");
      });
  };

  useEffect(() => {
    if (viewMode == 1) {
      setRightPanelSize(66);
    } else setRightPanelSize(33);
  }, [viewMode]);

  let blogBtn = null;
  if (!orgOffer?.blog_cnt && !orgOffer?.newsletter_cnt) {
    blogBtn = (
      <Button color="primary" variant="solid" title="No blog & newsletters exist!" onClick={() => setOpenBlogAndNewsletterModal(true)}>
        Blog & Newsletter
      </Button>
    );
  } else if (orgOffer?.blog_cnt && orgOffer?.newsletter_cnt) {
    blogBtn = (
      <Button color="green" variant="outlined" onClick={() => setOpenBlogAndNewsletterModal(true)}>
        Blog & Newsletter
      </Button>
    );
  } else {
    blogBtn = (
      <Button color="primary" variant="solid" ghost title="Blog or newsletters is missing!" onClick={() => setOpenBlogAndNewsletterModal(true)}>
        Blog & Newsletter
      </Button>
    );
  }

  return (
    <PageContainer /* title={`Org Offer #${offer_no} Details`} */ title={false}>
      <Card variant="borderless" style={{ marginBottom: 8, width: "100%" }}>
        <Row gutter={32}>
          <Col flex="200px">
            <Space size={32}>
              <Button
                type="primary"
                danger
                ghost
                title="Close this window..."
                onClick={() => {
                  window.close();
                }}
                icon={<CloseOutlined />}
              />
              <Button type="primary" onClick={() => setOpenCustomerContactSelectionModal(true)}>
                Send Email
              </Button>

              {blogBtn}
            </Space>
          </Col>
          <Col flex="auto">
            <Spin spinning={loadingExt} wrapperClassName="w-full">
              <ProForm
                formRef={formRefExt1}
                submitter={false}
                layout="inline"
                onValuesChange={(changedValues, values) => {
                  const hide = message.loading("Saving changed data...", 0);
                  updateOrCreateOfferExt({ offer_no: orgOffer?.offer_sid, ...changedValues })
                    .then((res) => {
                      hide();
                      loadOrgOfferDetail();
                      loadOfferExt();
                      message.success("Saved successfully.");
                    })
                    .catch(Util.error)
                    .finally(() => {
                      hide();
                    });
                }}
              >
                <ProFormRadio.Group name="status" label="Status" options={OfferExtStatusOptions} radioType="button" />
                <ProFormCheckbox name="is_brand" label="Brand" />
                <ProFormCheckbox name="is_b_group_appr" label="Is B-Group appr." />
              </ProForm>
            </Spin>
          </Col>
          <Col flex="">
            <ProFormSwitch
              name="viewMode"
              fieldProps={{
                checked: viewMode == 1,
                checkedChildren: "View",
                unCheckedChildren: "Edit",
                onChange(value) {
                  if (value) {
                    setFormData(formRefTpl.current?.getFieldsValue());
                  }
                  setViewMode(value ? 1 : 0);
                  Util.setSfValues("org_offer_detail_view_mode", value ? 1 : 0);
                },
              }}
              formItemProps={{ style: { marginBottom: 0 } }}
            />
          </Col>
        </Row>
      </Card>

      <Spin spinning={loading} wrapperClassName="w-full">
        <Splitter style={{ height: "calc(100vh - 140px)", boxShadow: "0 0 10px rgba(0, 0, 0, 0.1)", background: "#fff" }}>
          <Splitter.Panel>
            <ProForm layout="vertical" formRef={formRefTpl} isKeyPressSubmit className="search-form" submitter={false}>
              <Spin style={{ padding: 16 }} wrapperClassName="w-full" spinning={loading}>
                <div style={{ padding: 16 }}>
                  {viewMode === 0 ? (
                    <OfferTemplateLangFormPartial
                      renderSaveButton={() => {
                        return (
                          <Button type="primary" onClick={handleSave}>
                            Save
                          </Button>
                        );
                      }}
                    />
                  ) : (
                    <OfferTemplateLangViewPartial values={formData} />
                  )}
                </div>
              </Spin>
            </ProForm>
          </Splitter.Panel>
          <Splitter.Panel collapsible size={`${rightPanelSize}%`} max={`${rightPanelSize}%`}>
            <div style={{ padding: 16 }}>
              <Row justify="space-between" wrap={false}>
                <Col flex="auto">
                  <h3>Offer No: {offer_no}</h3>
                </Col>
                <Col></Col>
              </Row>
              <ConversationsMixedPanel
                isEditing={viewMode}
                loadOrgOfferDetail={loadOrgOfferDetail}
                orgOffer={orgOffer}
                offerExt={offerExt}
                loadOfferExt={loadOfferExt}
                loadingExt={loadingExt}
              />
            </div>
          </Splitter.Panel>
        </Splitter>
      </Spin>

      <CustomerContactSelectModalV2
        searchParams={{ offerNo: offer_no }}
        modalVisible={openCustomerContactSelectionModal}
        handleModalVisible={setOpenCustomerContactSelectionModal}
      />

      {!!orgOffer && !!offer_no && (
        <OfferBlogAndNewsletterModal
          modalVisible={openBlogAndNewsletterModal}
          handleModalVisible={setOpenBlogAndNewsletterModal}
          offer_no={offer_no}
          orgOffer={orgOffer}
          loadOrgOfferDetail={loadOrgOfferDetail}
        />
      )}
    </PageContainer>
  );
};

export default OrgOfferDetail;
