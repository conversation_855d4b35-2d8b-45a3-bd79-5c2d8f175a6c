import { <PERSON>, <PERSON>, Row, Space, Typography } from "antd";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { PageContainer } from "@ant-design/pro-layout";
import type { ProColumns, ActionType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";

import { CURRENT_YEAR, DEFAULT_PER_PAGE_PAGINATION } from "@/constants";
import Util, { DateRangeType, nf2, sn } from "@/util";
import type { ProFormInstance } from "@ant-design/pro-form";
import { ProFormSelect } from "@ant-design/pro-form";
import ProForm from "@ant-design/pro-form";
import { getSupplierListByPage, getSupplierLoInvoiceCheckStatsByPage } from "@/services/app/Supplier/supplier";
import { ProFormCheckbox, ProFormDigit, ProFormRadio } from "@ant-design/pro-components";
import useOrgAOptions from "@/hooks/BasicData/useOrgAOptions";
import SupplierStatisticsDetailListModal from "./components/SupplierStatisticsDetailListModal";

const FyListOptions = Util.dtBuildFyList(Util.dtCurrentFy(), 5);
const fyMonthsRange = Util.dtBuildRangesInFy(new Date(), false, true);

type RecordType = APITask.Supplier & APITask.FinDetail;
type SearchFormValueType = Partial<API.Supplier> & { view_mode: "monthly" | "yearly" };
const DefaultSearchFormValues = {
  view_mode: "monthly",
  years_count: 6,
};

const SupplierStatistics: React.FC = () => {
  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<{ supplier_id?: number; y?: number; ym?: string }>();

  const { orgAOptionsKv } = useOrgAOptions();

  // stats related
  const [statsMode, setStatsMode] = useState("EK_Summe"); // Stats mode to show Summe or EK_Summe or Ertrag from WHC_Task
  const [viewMode, setViewMode] = useState("monthly");
  const [yearsCount, setYearsCount] = useState(6);
  const [months, setMonths] = useState<DateRangeType[]>(fyMonthsRange);

  // Detail modal
  const [detailModalVisible, handleDetailModalVisible] = useState<boolean>(false);

  const columnsMonthly: ProColumns<RecordType>[] = useMemo(() => {
    return [
      {
        title: "Avg",
        dataIndex: "bal_prev",
        search: false,
        width: 70,
        align: "right",
        tooltip: "AVG in 12 months in old years + curr.Qty.ofMonth in cur year. (current month is excluded)",
        className: "bl2 b-gray text-sm c-grey",
        render: (__: any, record: any) => {
          const value = sn(record[`${statsMode}_prev`]);
          return nf2(value);
        },
      },
      ...(months.map((month, mInd) => ({
        title: month.title,
        dataIndex: "bal_" + mInd,
        search: false,
        width: 85,
        align: "right",
        className: mInd == 0 ? "bl2 b-gray cursor-pointer" : "cursor-pointer",
        render: (__: any, record: any) => {
          const value = sn(record[`${statsMode}_${mInd}`]);
          return nf2(value);
        },
        onCell: (record: RecordType) => {
          return {
            onClick: () => {
              setCurrentRow({ supplier_id: record.id, ym: month.from?.substring(0, 7) });
              handleDetailModalVisible(true);
            },
          };
        },
      })) as any),
      {
        title: "Total",
        dataIndex: "bal_total",
        search: false,
        width: 85,
        align: "right",
        className: "bl2 b-gray",
        render: (__: any, record: any) => {
          const value = sn(record[`${statsMode}_total`]);
          return nf2(value);
        },
      },
    ];
  }, [months, statsMode]);

  const YEARS = useMemo(() => {
    return Util.dtBuildFyList(Util.dtCurrentFy(), yearsCount, true);
  }, [yearsCount]);

  console.log(YEARS);

  const columnsYearly: ProColumns<RecordType>[] = useMemo(() => {
    return [
      ...(YEARS.map((x, mInd) => ({
        title: x.label,
        dataIndex: "bal_" + mInd,
        search: false,
        width: 85,
        align: "right",
        className: mInd == 0 ? "bl2 b-gray cursor-pointer" : "cursor-pointer",
        render: (__: any, record: any) => {
          const value = sn(record[`${statsMode}_${mInd}`]);
          return nf2(value);
        },
        onCell: (record: RecordType) => {
          return {
            onClick: () => {
              setCurrentRow({ supplier_id: sn(record.id), y: sn(x.value) });
              handleDetailModalVisible(true);
            },
          };
        },
      })) as any),
      {
        title: () => {
          return <span className="text-sm c-grey">{CURRENT_YEAR + 1}</span>;
        },
        dataIndex: "bal_next",
        search: false,
        width: 70,
        align: "right",
        className: "bl2 b-gray text-sm c-grey cursor-pointer",
        render: (__: any, record: any) => {
          const value = sn(record[`${statsMode}_next`]);
          return nf2(value);
        },
        onCell: (record: RecordType) => {
          return {
            onClick: () => {
              setCurrentRow({ supplier_id: sn(record.id), y: CURRENT_YEAR + 1 });
              handleDetailModalVisible(true);
            },
          };
        },
      },
      {
        title: "Total",
        dataIndex: "bal_total",
        search: false,
        width: 85,
        align: "right",
        className: "bl2 b-gray",
        render: (__: any, record: any) => {
          const value = sn(record[`${statsMode}_total`]);
          return nf2(value);
        },
      },
    ];
  }, [YEARS, statsMode]);

  const columns: ProColumns<RecordType>[] = useMemo(
    () => [
      {
        title: "OrgA",
        dataIndex: ["org_a"],
        width: 50,
        align: "center",
      },
      {
        title: "Name",
        dataIndex: "name",
        width: 250,
        showSorterTooltip: false,
        render(__, entity) {
          const supplier = entity.supp_supplier;

          return (
            <Row wrap={false} gutter={4}>
              <Col flex="auto">
                {supplier ? (
                  <Typography.Link copyable={false} href={`${PUBLIC_PATH}suppliers/detail/${supplier.id}`} target="_blank">
                    {entity.name || " - "}
                  </Typography.Link>
                ) : (
                  <Typography.Text copyable={false}>{entity.name}</Typography.Text>
                )}
              </Col>
            </Row>
          );
        },
      },
      ...(viewMode == "yearly" ? columnsYearly : columnsMonthly),
      {
        dataIndex: "option",
        valueType: "option",
      },
    ],
    [columnsMonthly, columnsYearly, viewMode],
  );

  useEffect(() => {
    if (Object.keys(orgAOptionsKv)?.length) {
      const formValues = Util.getSfValues("supplier_stats", DefaultSearchFormValues);
      if (!("in_org_a" in formValues)) {
        searchFormRef.current?.setFieldValue("in_org_a", Object.keys(orgAOptionsKv));
        actionRef.current?.reload();
      }
    }
  }, [orgAOptionsKv]);

  useEffect(() => {
    const lastSf = Util.getSfValues("supplier_stats", DefaultSearchFormValues);
    if (!lastSf.fy) {
      lastSf.fy = Util.dtCurrentFy();
    }
    setMonths(Util.dtBuildRangesInFy(+lastSf.fy, false, true));
    searchFormRef.current?.setFieldsValue(lastSf);
    Util.setSfValues(lastSf);
  }, []);

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            render(props, dom) {
              return <div style={{ marginLeft: "auto" }}>{dom}</div>;
            },
            searchConfig: { submitText: "Search" },
            submitButtonProps: { loading, htmlType: "submit", style: { marginLeft: 8 } },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => {
              searchFormRef.current?.resetFields();
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormCheckbox.Group
            name="in_org_a"
            label="Org A"
            valueEnum={orgAOptionsKv}
            fieldProps={{
              onChange(checkedValue) {
                actionRef.current?.reload();
              },
            }}
          />

          <ProFormRadio.Group
            options={[
              { value: "Summe", label: "Turnover" },
              { value: "EK_Summe", label: "BP" },
              { value: "Ertrag", label: "Ertrag" },
            ]}
            radioType="button"
            fieldProps={{
              defaultValue: statsMode,
              buttonStyle: "solid",
              onChange(e) {
                setStatsMode(e.target.value);
              },
            }}
          />

          <ProFormRadio.Group
            name="view_mode"
            options={[
              { value: "monthly", label: "Monthly" },
              { value: "yearly", label: "Yearly" },
            ]}
            radioType="button"
            fieldProps={{
              value: viewMode,
              buttonStyle: "solid",
              onChange(e) {
                setViewMode(e.target.value);
                actionRef.current?.reload();
              },
            }}
          />
          {viewMode == "monthly" && (
            <ProFormSelect
              key="year"
              name="fy"
              label="Fiscal Year"
              width={100}
              placeholder="Fiscal year"
              formItemProps={{ style: { marginBottom: 0 } }}
              options={FyListOptions}
              fieldProps={{
                onChange(value, option) {
                  const fy = value ?? Util.dtCurrentFy();
                  const newMonths = Util.dtBuildRangesInFy(+fy, false, true);
                  setMonths(newMonths);
                  actionRef.current?.reload();
                },
              }}
            />
          )}
          {viewMode == "yearly" && (
            <ProFormDigit
              name="years_count"
              label="Last N Year"
              width={100}
              fieldProps={{
                value: yearsCount,
                onChange(value) {
                  setYearsCount(sn(value));
                  actionRef.current?.reload();
                },
              }}
              formItemProps={{ style: { marginBottom: 0 } }}
            />
          )}
          <ProFormSelect
            name="supp_supplier_id"
            label="Supplier"
            width="md"
            showSearch
            allowClear
            request={async (params) => {
              return getSupplierListByPage(params)
                .then((res) => {
                  return res.data?.map((x) => ({ ...x, value: x.id, label: x.name }));
                })
                .catch((err) => {
                  Util.error(err);
                  return [];
                });
            }}
            fieldProps={{
              onChange(value, option) {
                actionRef.current?.reload();
              },
            }}
          />
          {/* <ProFormText name={"keyWords"} label="KeyWords" width={200} placeholder={"Search by Company Name / Contacts / Email"} /> */}
        </ProForm>
      </Card>

      <ProTable<RecordType, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>Supplier Statistics</span>
          </Space>
        }
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{
          defaultPageSize: sn(Util.getSfValues("supplier_stats_p")?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues("supplier_stats", searchFormValues);
          Util.setSfValues("supplier_stats_p", params);

          setLoading(true);
          return getSupplierLoInvoiceCheckStatsByPage(
            {
              ...params,
              ...searchFormValues,
              months: months.map((x) => ({ from: x.from?.substring(0, 7) })),
              with: "org_supplier,contacts,address,address.country,ext,ext.user,meta,metaLabel,calls_phone_count",
            },
            sort,
            filter,
          )
            .then((res) => {
              return res as RecordType;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        tableAlertRender={false}
        columnEmptyText=""
        locale={{ emptyText: <></> }}
      />

      <SupplierStatisticsDetailListModal handleModalVisible={handleDetailModalVisible} modalVisible={detailModalVisible} searchParams={currentRow} />
    </PageContainer>
  );
};

export default SupplierStatistics;
