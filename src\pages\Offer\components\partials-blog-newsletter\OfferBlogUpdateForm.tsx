import HtmlEditor from "@/components/HtmlEditor";
import { LS_TOKEN_NAME } from "@/constants";
import { deleteFile } from "@/services/app/File/file";
import { getOfferBlogByOfferNo, updateOrCreateOfferBlogByOfferNo } from "@/services/app/Offer/offer-blog";
import Util, { sn } from "@/util";
import { ProForm, ProFormInstance, ProFormText, ProFormUploadDragger } from "@ant-design/pro-components";
import { Button, message, Modal, Space, Spin } from "antd";
import { RcFile } from "antd/es/upload";
import { useCallback, useEffect, useRef, useState } from "react";
import styles from "./OfferBlogUpdateForm.less";
import { UploadFile } from "antd/lib";

type FormValueType = API.OfferBlog;

export type OfferBlogUpdateFormProps = {
  offer_no: string;
  reloadTick: number;
  onSubmit?: (formData: FormValueType) => void;
  loadOrgOfferDetail?: () => void;
};

const OfferBlogUpdateForm: React.FC<OfferBlogUpdateFormProps> = ({ offer_no, onSubmit, reloadTick, loadOrgOfferDetail }) => {
  const formRef = useRef<ProFormInstance<FormValueType>>();

  const [loading, setLoading] = useState<boolean>(false);

  const loadOfferBlog = useCallback(() => {
    setLoading(true);
    getOfferBlogByOfferNo(offer_no, { with: "files" })
      .then((res) => {
        console.log("blog", res);
        formRef.current?.setFieldsValue(res);
      })
      .catch(Util.error)
      .finally(() => setLoading(false));
  }, [offer_no]);

  useEffect(() => {
    loadOfferBlog();
  }, [loadOfferBlog]);

  useEffect(() => {
    if (reloadTick) {
      loadOfferBlog();
    }
  }, [reloadTick, loadOfferBlog]);

  return (
    <div className={styles.offerBlogUpdateForm}>
      <Spin spinning={loading} style={{ width: "100%" }}>
        <ProForm<FormValueType> layout="vertical" formRef={formRef} isKeyPressSubmit className="search-form" grid submitter={false}>
          <ProFormText name={"title"} label={"Title"} wrapperCol={{ span: 24 }} />
          <ProForm.Item
            name={"description"}
            label={<>Description</>}
            style={{ width: "100%" }}
            labelCol={undefined}
            wrapperCol={{ span: 24 }}
            rules={[
              {
                required: true,
                message: "Body content is required",
              },
            ]}
          >
            <HtmlEditor id={`offer_blog_description`} enableTextModule hideMenuBar toolbarMode={2} height={300} />
          </ProForm.Item>

          <ProFormUploadDragger
            name={["files"]}
            label={"Blog Files"}
            title={false}
            description="Please select blog files or drag & drop"
            wrapperCol={{ span: 24 }}
            fieldProps={{
              multiple: true,
              listType: "picture",
              name: "files",
              style: { marginBottom: 24 },
              headers: {
                Authorization: `Bearer ${localStorage.getItem(LS_TOKEN_NAME)}`,
              },
              beforeUpload: (file: RcFile, fileList: RcFile[]) => {
                return false;
              },
              onRemove: async (file: API.File) => {
                if (file.id) {
                  const { confirm } = Modal;
                  return new Promise((resolve, reject) => {
                    confirm({
                      title: "Are you sure you want to delete?",
                      onOk: async () => {
                        resolve(true);
                        const hide = message.loading(`Deleting a file '${file.file_name}'.`, 0);
                        const res = await deleteFile(sn(file.id));
                        hide();
                        if (res) {
                          message.success(`Deleted successfully!`);
                        } else {
                          Util.error(`Delete failed, please try again!`);
                        }

                        return res;
                      },
                      onCancel: () => {
                        reject(true);
                      },
                    });
                  });
                } else {
                  return true;
                }
              },
            }}
          />

          <Space size={16} style={{ marginLeft: "auto" }}>
            <Button
              type="default"
              onClick={() => {
                formRef.current?.resetFields();
              }}
            >
              Reset
            </Button>
            <Button
              type="primary"
              onClick={() => {
                const formValues = formRef.current?.getFieldsValue();
                const formData = new FormData();
                formData.set("offer_no", offer_no);
                formData.set("title", formValues?.title || "");
                formData.set("description", formValues?.description || "");
                if (formValues?.files?.length) {
                  formValues?.files.forEach((file, ind) => {
                    console.log(file);
                    if (!sn(file.uid)) {
                      formData.set(`files[${ind}]`, (file as UploadFile).originFileObj || "");
                    }
                  });
                }

                const hide = message.loading("Saving blog data...", 0);
                updateOrCreateOfferBlogByOfferNo(formData)
                  .then((res) => {
                    hide();
                    message.success("Saved successfully.");
                    if (onSubmit) onSubmit(res);
                    loadOfferBlog();
                    loadOrgOfferDetail?.();
                  })
                  .catch(Util.error)
                  .finally(() => {
                    hide();
                  });
              }}
            >
              Save Blog
            </Button>
          </Space>
        </ProForm>
      </Spin>
    </div>
  );
};
export default OfferBlogUpdateForm;
