export const LS_PREFIX = 'WHC_SUPP_';
export const LS_TOKEN_NAME = `${LS_PREFIX}TOKEN`;
export const DEFAULT_PER_PAGE_PAGINATION = 100;
export const AC_PER_PAGE_PAGINATION = 100;
export const EURO = '€';

export const DT_MAX_DATE = '2099-12-31';
export const DT_MIN_DATE = '1900-01-01';

export const DT_FORMAT_MY = 'MM.YYYY';
export const DT_FORMAT_DMY = 'DD.MM.YYYY';
export const DT_FORMAT_DM = 'DD.MM.';
export const DT_FORMAT_DMY_HHMMSS = 'DD.MM.YYYY';
export const DT_FORMAT_YMD = 'YYYY-MM-DD';
export const DT_FORMAT_TIME_MAX = '23:59:59';
export const DT_FORMAT_TIME_MAX_S = ' 23:59:59';
export const DT_FORMAT_LIST = [DT_FORMAT_DMY, DT_FORMAT_YMD];

export const DAYS = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

export const CURRENT_YEAR = new Date().getFullYear();

export enum UserRole {
  ADMIN = 1,
  USER_FULL = 2,
  USER_NORMAL = 4,
  USER_LIGHT = 8,
}

export const UserRoleOptions = [
  { value: UserRole.USER_LIGHT, label: 'User Light' },
  { value: UserRole.USER_NORMAL, label: 'User Normal' },
  { value: UserRole.USER_FULL, label: 'User Full' },
  { value: UserRole.ADMIN, label: 'Admin' },
];

export enum UserStatus {
  DISABLED = 0,
  ENABLED = 1,
  BLOCKED = 2,
}

export enum AlertStatus {
  'success' = 'success',
  'error' = 'error',
  'info' = 'info',
}


/**
 * General status
 */
export enum Status {
  'ACTIVE' = 1,
  'INACTIVE' = 0,
}

export const StatusOptions = [
  { value: Status.ACTIVE, label: 'Active' },
  { value: Status.INACTIVE, label: 'Inactive' },
];

export const YNOptions = [
  { value: 1, label: 'Ja' },
  { value: 0, label: 'Nein' },
];

export const YNOptionsStr = [
  { value: '1', label: 'Ja' },
  { value: '0', label: 'Nein' },
];


export enum TaskStatus {
  Open = 0,
  InProgress = 1,
  Done = 2,
}

export const TaskStatusOptions = [
  { value: TaskStatus.Open, label: 'Open' },
  { value: TaskStatus.InProgress, label: 'In Progress' },
  { value: TaskStatus.Done, label: 'Done' },
];

/**
 * Dict types for creatable system configuration
 */
export enum DictType {
  SysConfig = 'sys config',
  EmailConfig = 'Email Config', // Email config
}

export const DictTypeKv: Record<DictType | string, string> = {
  [DictType.SysConfig]: 'System Config',
  [DictType.EmailConfig]: 'Email Config',
};

export const DictTypeCreatableKv: Record<DictType | string, string> = {

};

export enum DictCode {
  EMAIL_FROM = 'EMAIL_FROM',
  OFFER_SALUTATION_DE = 'OFFER_SALUTATION_DE',
  OFFER_SALUTATION_EN = 'OFFER_SALUTATION_EN',
  OFFER_FOOTER_DE = 'OFFER_FOOTER_DE',
  OFFER_FOOTER_EN = 'OFFER_FOOTER_EN',
  OFFER_HIDE_COMPANY = 'OFFER_HIDE_COMPANY',
  EK_VK_LOCAL_FILE_PATH = 'EK_VK_LOCAL_FILE_PATH',
}

export enum ShippingAddressStatus {
  Open = 'Open',
  Problem = 'Problem',
  Done = 'Done',
}





/**
 * Category of SysLog
 */
export enum SysLogCategory {
  CATEGORY_LOGIN = 'login',
  CATEGORY_EMAIL_TRACKING = 'Email Tracking supp',
}

export const SysLogCategoryOptions = Object.entries(SysLogCategory).map((x, value) => {
  return {
    value: x[1],
    label: x[1],
  };
});


export enum SupplierCallType {
  Phone = 1,
  Notes = 2,
  Email = 3,
  Offer = 4,
  Meeting = 5,
}

export const SupplierCallTypeKv: Record<number, string> = {
  [SupplierCallType.Phone]: 'Phone',
  [SupplierCallType.Notes]: 'Notes',
  [SupplierCallType.Email]: 'Email',
  [SupplierCallType.Offer]: 'Offer',
  [SupplierCallType.Meeting]: 'Meeting',
}

export enum SupplierCallDirection {
  Out = 'O',
  In = 'I',
  WHC = 'W',
}

export const SupplierCallDirectionKv: Record<string, string> = {
  [SupplierCallDirection.Out]: 'Out',
  [SupplierCallDirection.In]: 'In',
  [SupplierCallDirection.WHC]: 'WHC',
}


export const PageSizeOptionsOnCard3 = ['12', '24', '48', '60', '120'];

export const THIS_YEAR = new Date().getFullYear();