import { getEmail } from "@/services/app/Email/email";
import Util, { nl2br } from "@/util";
import { FileOutlined, MailOutlined } from "@ant-design/icons";
import type { ProDescriptionsActionType } from "@ant-design/pro-descriptions";
import ProDescriptions from "@ant-design/pro-descriptions";
import { Avatar, Modal, Space, Typography, Image } from "antd";
import { Dispatch, SetStateAction, useCallback, useEffect, useRef, useState } from "react";

export type CustomerOfferEmailViewModalProps = {
  emailId: number;
  emailAddr?: string;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  options?: { width?: number; title?: string };
};

const CustomerOfferEmailViewModal: React.FC<CustomerOfferEmailViewModalProps> = (props) => {
  const { emailId, emailAddr, modalVisible, handleModalVisible, options } = props;
  const actionRef = useRef<ProDescriptionsActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [email, setEmail] = useState<API.Email>();

  const loadEmailDetail = useCallback(() => {
    setLoading(true);
    getEmail(emailId)
      .then((res) => {
        setEmail(res);
      })
      .catch(Util.error)
      .finally(() => setLoading(false));
  }, [emailId]);

  useEffect(() => {
    if (modalVisible) {
      loadEmailDetail();
    }
  }, [emailId, loadEmailDetail, modalVisible]);

  return (
    <Modal
      title={"Email viewer"}
      width={options?.width ?? 1200}
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      footer={false}
      loading={loading}
    >
      <>
        <div className="text-sm" style={{ right: 24, top: 48, position: "absolute" }}>
          {Util.dtToDMYHHMM(email?.date)}
        </div>
        <ProDescriptions
          title={
            <div>
              <MailOutlined className="c-blue" />
              &nbsp;&nbsp;
              {email?.subject}
            </div>
          }
          bordered
          actionRef={actionRef}
          column={2}
          request={async () => {
            //   return getEmail(emailId);
            return { data: email };
          }}
        >
          <ProDescriptions.Item label="From" dataIndex="sender" span={2} />
          <ProDescriptions.Item label="To" dataIndex="receiver" span={2} />
          <ProDescriptions.Item span={2}>
            {
              <div
                dangerouslySetInnerHTML={{
                  __html: email?.text_html ? email?.text_html : nl2br(email?.text_plain),
                }}
              />
            }
          </ProDescriptions.Item>
          <ProDescriptions.Item span={2}>
            <Space size={24}>
              {email?.attachments?.map?.((a) => {
                const fileUrl = `${API_URL}/api/download?key=${a.file_path}`;
                return (
                  <Space key={a.name} direction="vertical">
                    {a.mime_type?.includes("image") ? (
                      <Avatar shape="square" size={128} src={<Image src={fileUrl} />} />
                    ) : (
                      <FileOutlined style={{ fontSize: 128, color: "grey" }} />
                    )}
                    <Typography.Link href={fileUrl} ellipsis target="_blank" className="text-sm" style={{ textAlign: "center", maxWidth: 128 }}>
                      {a.org_name}
                    </Typography.Link>
                  </Space>
                );
              })}
            </Space>
          </ProDescriptions.Item>
        </ProDescriptions>
      </>
    </Modal>
  );
};

export default CustomerOfferEmailViewModal;
