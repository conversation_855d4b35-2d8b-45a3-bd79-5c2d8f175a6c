import type { Dispatch, SetStateAction } from "react";
import React, { useEffect, useRef } from "react";
import type { ProFormInstance } from "@ant-design/pro-form";
import { ModalForm } from "@ant-design/pro-form";
import { addSupplier } from "@/services/app/Supplier/supplier";
import { Col, message, Space } from "antd";
import Util from "@/util";
import { ProFormDigit, ProFormSelect, ProFormText } from "@ant-design/pro-components";
import { useModel } from "@umijs/max";
import { countryOptions, getCountriesDEOptions } from "@/services/app/countries";

const handleAdd = async (fields: API.Supplier) => {
  const hide = message.loading("Adding...", 0);
  const data = { ...fields };
  try {
    await addSupplier(data);
    message.success("Added successfully");
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  initialValues?: Partial<API.Supplier>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Supplier) => void;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const { initialValues, modalVisible, handleModalVisible, onSubmit } = props;

  const { initialState } = useModel("@@initialState");

  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (modalVisible) {
      formRef.current?.resetFields();
      formRef.current?.setFieldValue(["address", "country_id"], "DE");
    }
  }, [initialState?.currentUser?.initials, initialValues, modalVisible]);

  return (
    <ModalForm<API.Supplier & { copy_to_sk?: boolean }>
      title={"New Supplier"}
      width="1200px"
      open={modalVisible}
      onOpenChange={handleModalVisible}
      layout="horizontal"
      grid
      labelCol={{ flex: "100px" }}
      wrapperCol={{ flex: "auto" }}
      formRef={formRef}
      onFinish={async (value) => {
        Util.setSfValues("f_new", { ...value, note: null });

        const success = await handleAdd({
          ...value,
        } as API.Supplier);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) onSubmit(value);
          handleModalVisible(false);
        }
      }}
    >
      <Col span={16}>
        <ProFormText name="internal_name" label="Internal Name" />
      </Col>
      <Col span={24}>
        <ProFormText name="name" label="Company" required rules={[{ message: "Company is required!", required: true }]} />
      </Col>
      {/* <Col span={7}>
        <ProFormText name="org_a" label="Org A" width="xs" disabled />
      </Col>
      <Col span={7}>
        <ProFormDigit name="org_b" label="Org B" width="xs" disabled />
      </Col> */}
      <Col span={14}>
        <ProFormText name={["address", "street1"]} label="Street 1" />
        <ProFormText name={["address", "street2"]} label="Street 2" />
        <ProFormText name={["address", "postcode"]} label="Postcode" width="sm" />
        <ProFormText
          name={["address", "city"]}
          label="City"
          width="md"
          required
          rules={[
            {
              required: true,
              message: "City is required",
            },
          ]}
        />

        <ProFormText name={["address", "region"]} label="State" width="md" />
        <ProFormSelect
          name={["address", "country_id"]}
          label="Country"
          options={countryOptions}
          showSearch
          allowClear
          width="md"
          required
          rules={[
            {
              required: true,
              message: "Country is required",
            },
          ]}
        />
      </Col>
      <Col span={10}>
        <ProFormText name={["address", "telephone"]} label="Telephone" />
        <ProFormText name={["address", "fax"]} label="Fax" />
      </Col>
    </ModalForm>
  );
};

export default CreateForm;
