declare namespace APITask {
  type Supplier = {
    id?: string;
    name?: string;
    description?: string;
    notes?: string;
    pallet?: string;
    org_a?: string;
    org_b?: string;
    supp_supplier_id?: number;
  } & API.TimestampData & {
    supp_supplier?: API.Supplier;
  };

  type LoInvoiceCheck = {
    lo_id?: string;
    lo_no?: string;
    lo_type?: LoVoucherType;
    lo_status?: string;
    lo_document_file_id?: string;
    lo_version?: number;
    org_customer_id?: number;
    lo_cust_no?: number;
    lo_contact_id?: string;
    lo_voucher_date?: string;
    total_gross_amount?: number;
    detail?: Record<string, any>;
    lo_created_on?: string;
    lo_updated_on?: string;
    lo_contact_name?: string;
    lo_archived?: boolean;
    lo_currency?: string;
    is_checked?: number;  // Checked status
    is_stat_checked?: number;  // Checked status in Invoice Stat.
    is_exported?: number;  // is it exported to stat table(fin_detail)
    note?: string;  // user's note
    order_out_nos?: number[];

    org_customer?: Customer;
    order_out?: OrderOut;
  } & {
    children?: any[];
  }

  type FinDetail = {
    id?: number;

    Field1?: string;
    Re_Nr?: number;
    voucher_no?: string;
    Kunde?: string;
    Datum?: string;
    Artikelnummer?: number;
    Beschreibung?: string;
    Gepackt?: number;
    Order?: number;
    Preis?: number;
    Summe?: number;
    EK?: number;
    EK_Summe?: number;
    Ertrag?: number;
    Kategorie?: string;

    supplier_name?: string | null;
    supplier_org_a?: string | null;
    hs_code?: string;
    net_weight?: number;
    net_unit_weight?: number;

    ek_kg_price?: number;
    ek_kg_sum?: number;
    vk_kg_price?: number;
    vk_kg_sum?: number;

    import_source?: string;
    import_source_id?: number;
    import_ref?: string;
    import_ref_id?: number;
    order_ref?: string;
    order_ref_id?: number;

    // relation
    order_ref?: OrderOut | OrderIn;
  } & { uid?: string };
}